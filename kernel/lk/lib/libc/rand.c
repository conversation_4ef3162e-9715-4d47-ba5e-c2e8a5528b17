/*
 * Copyright (c) 2008-2014 <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files
 * (the "Software"), to deal in the Software without restriction,
 * including without limitation the rights to use, copy, modify, merge,
 * publish, distribute, sublicense, and/or sell copies of the Software,
 * and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#include <rand.h>
#include <sys/types.h>

static unsigned int randseed = KERNEL_LIBC_RANDSEED;

void srand(unsigned int seed)
{
    randseed = seed;
}

void libc_rctee_rand_add_entropy(const void *buf, size_t len)
{
    if (len == 0)
        return;

    uint32_t enp = 0;
    for (size_t i = 0; i < len; i++) {
        enp ^= ((enp << 8) | (enp >> 24)) ^ ((const uint8_t *)buf)[i];
    }

    randseed ^= enp;
}

/* This is expected to overflow sometimes */
__attribute__((no_sanitize("unsigned-integer-overflow")))
int rand(void)
{
    return (int)(randseed = randseed * 1664525 + 1013904223);
}
