/*
 * Copyright (c) 2008-2015 <PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files
 * (the "Software"), to deal in the Software without restriction,
 * including without limitation the rights to use, copy, modify, merge,
 * publish, distribute, sublicense, and/or sell copies of the Software,
 * and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
#pragma once

#include <stddef.h>
#include <sys/types.h>
#include <compiler.h>

__BEGIN_CDECLS;

/* standard heap definitions */
void *malloc(size_t size) __MALLOC __WARN_UNUSED_RESULT;
void *memalign(size_t boundary, size_t size) __MALLOC __WARN_UNUSED_RESULT;
int posix_memalign(void** res, size_t align, size_t size);
void *calloc(size_t count, size_t size) __WARN_UNUSED_RESULT;
void *realloc(void *ptr, size_t size) __WARN_UNUSED_RESULT;
void free(void *ptr);

void heap_init(void);

/* tell the heap to return any free pages it can find */
void heap_trim(void);

__END_CDECLS;
