# create a separate list of objects per source type

# If a source  file is located under $(BUILDDIR) directory
# a corresponding object will be generated next to it
MODULE_INPLACE_SRCS := $(filter $(BUILDDIR)/%,$(MODULE_SRCS))
MODULE_OUTPLACE_SRCS := $(filter-out $(BUILDDIR)/%,$(MODULE_SRCS))

MODULE_INPLACE_SRCS_FIRST := $(filter $(BUILDDIR)/%,$(MODULE_SRCS_FIRST))
MODULE_OUTPLACE_SRCS_FIRST := $(filter-out $(BUILDDIR)/%,$(MODULE_SRCS_FIRST))

MODULE_CSRCS := $(filter %.c,$(MODULE_OUTPLACE_SRCS))
MODULE_CPPSRCS := $(filter %.cpp,$(MODULE_OUTPLACE_SRCS))
MODULE_CCSRCS := $(filter %.cc,$(MODULE_OUTPLACE_SRCS))
MODULE_ASMSRCS := $(filter %.S,$(MODULE_OUTPLACE_SRCS))

MODULE_COBJS := $(call TOBUILDDIR,$(patsubst %.c,%.o,$(MODULE_CSRCS)))
MODULE_CPPOBJS := $(call TOBUILDDIR,$(patsubst %.cpp,%.o,$(MODULE_CPPSRCS)))
MODULE_CCOBJS := $(call TOBUILDDIR,$(patsubst %.cc,%.o,$(MODULE_CCSRCS)))
MODULE_ASMOBJS := $(call TOBUILDDIR,$(patsubst %.S,%.o,$(MODULE_ASMSRCS)))

MODULE_CSRCS_FIRST := $(filter %.c,$(MODULE_OUTPLACE_SRCS_FIRST))
MODULE_CPPSRCS_FIRST := $(filter %.cpp,$(MODULE_OUTPLACE_SRCS_FIRST))
MODULE_CCSRCS_FIRST := $(filter %.cc,$(MODULE_OUTPLACE_SRCS_FIRST))
MODULE_ASMSRCS_FIRST := $(filter %.S,$(MODULE_OUTPLACE_SRCS_FIRST))

MODULE_COBJS_FIRST := $(call TOBUILDDIR,$(patsubst %.c,%.o,$(MODULE_CSRCS_FIRST)))
MODULE_CPPOBJS_FIRST := $(call TOBUILDDIR,$(patsubst %.cpp,%.o,$(MODULE_CPPSRCS_FIRST)))
MODULE_CCOBJS_FIRST := $(call TOBUILDDIR,$(patsubst %.cc,%.o,$(MODULE_CCSRCS_FIRST)))
MODULE_ASMOBJS_FIRST := $(call TOBUILDDIR,$(patsubst %.S,%.o,$(MODULE_ASMSRCS_FIRST)))

# same for INPLACE sources
MODULE_INPLACE_CSRCS := $(filter %.c,$(MODULE_INPLACE_SRCS))
MODULE_INPLACE_CPPSRCS := $(filter %.cpp,$(MODULE_INPLACE_SRCS))
MODULE_INPLACE_CCSRCS := $(filter %.cc,$(MODULE_INPLACE_SRCS))
MODULE_INPLACE_ASMSRCS := $(filter %.S,$(MODULE_INPLACE_SRCS))

MODULE_INPLACE_COBJS := $(patsubst %.c,%.o,$(MODULE_INPLACE_CSRCS))
MODULE_INPLACE_CPPOBJS := $(patsubst %.cpp,%.o,$(MODULE_INPLACE_CPPSRCS))
MODULE_INPLACE_CCOBJS := $(patsubst %.cc,%.o,$(MODULE_INPLACE_CCSRCS))
MODULE_INPLACE_ASMOBJS := $(patsubst %.S,%.o,$(MODULE_INPLACE_ASMSRCS))

MODULE_INPLACE_CSRCS_FIRST := $(filter %.c,$(MODULE_INPLACE_SRCS_FIRST))
MODULE_INPLACE_CPPSRCS_FIRST := $(filter %.cpp,$(MODULE_INPLACE_SRCS_FIRST))
MODULE_INPLACE_CCSRCS_FIRST := $(filter %.cc,$(MODULE_INPLACE_SRCS_FIRST))
MODULE_INPLACE_ASMSRCS_FIRST := $(filter %.S,$(MODULE_INPLACE_SRCS_FIRST))

MODULE_INPLACE_COBJS_FIRST := $(patsubst %.c,%.o,$(MODULE_INPLACE_CSRCS_FIRST))
MODULE_INPLACE_CPPOBJS_FIRST := $(patsubst %.cpp,%.o,$(MODULE_INPLACE_CPPSRCS_FIRST))
MODULE_INPLACE_CCOBJS_FIRST := $(patsubst %.cc,%.o,$(MODULE_INPLACE_CCSRCS_FIRST))
MODULE_INPLACE_ASMOBJS_FIRST := $(patsubst %.S,%.o,$(MODULE_INPLACE_ASMSRCS_FIRST))

# do the same thing for files specified in arm override mode
MODULE_ARM_CSRCS := $(filter %.c,$(MODULE_ARM_OVERRIDE_SRCS))
MODULE_ARM_CPPSRCS := $(filter %.cpp,$(MODULE_ARM_OVERRIDE_SRCS))
MODULE_ARM_CCSRCS := $(filter %.cc,$(MODULE_ARM_OVERRIDE_SRCS))
MODULE_ARM_ASMSRCS := $(filter %.S,$(MODULE_ARM_OVERRIDE_SRCS))

MODULE_ARM_COBJS := $(call TOBUILDDIR,$(patsubst %.c,%.o,$(MODULE_ARM_CSRCS)))
MODULE_ARM_CPPOBJS := $(call TOBUILDDIR,$(patsubst %.cpp,%.o,$(MODULE_ARM_CPPSRCS)))
MODULE_ARM_CCOBJS := $(call TOBUILDDIR,$(patsubst %.cc,%.o,$(MODULE_ARM_CCSRCS)))
MODULE_ARM_ASMOBJS := $(call TOBUILDDIR,$(patsubst %.S,%.o,$(MODULE_ARM_ASMSRCS)))

MODULE_OBJS := $(MODULE_COBJS) \
               $(MODULE_CPPOBJS) \
               $(MODULE_CCOBJS) \
               $(MODULE_ASMOBJS) \
               $(MODULE_ARM_COBJS) \
               $(MODULE_ARM_CPPOBJS) \
               $(MODULE_ARM_CCOBJS) \
               $(MODULE_ARM_ASMOBJS) \
               $(MODULE_INPLACE_COBJS) \
               $(MODULE_INPLACE_CPPOBJS) \
               $(MODULE_INPLACE_CCOBJS) \
               $(MODULE_INPLACE_ASMOBJS) \

MODULE_INIT_OBJS := \
	$(MODULE_COBJS_FIRST) \
	$(MODULE_CPPOBJS_FIRST) \
	$(MODULE_CCOBJS_FIRST) \
	$(MODULE_ASMOBJS_FIRST) \

MODULE_COBJS += $(MODULE_COBJS_FIRST)
MODULE_CPPOBJS += $(MODULE_CPPOBJS_FIRST)
MODULE_CCOBJS += $(MODULE_CCOBJS_FIRST)
MODULE_ASMOBJS += $(MODULE_ASMOBJS_FIRST)

MODULE_INPLACE_COBJS += $(MODULE_INPLACE_COBJS_FIRST)
MODULE_INPLACE_CPPOBJS += $(MODULE_INPLACE_CPPOBJS_FIRST)
MODULE_INPLACE_CCOBJS += $(MODULE_INPLACE_CCOBJS_FIRST)
MODULE_INPLACE_ASMOBJS += $(MODULE_INPLACE_ASMOBJS_FIRST)

#$(info MODULE_CSRCS = $(MODULE_CSRCS))
#$(info MODULE_CPPSRCS = $(MODULE_CPPSRCS))
#$(info MODULE_ASMSRCS = $(MODULE_ASMSRCS))

#$(info MODULE_OBJS = $(MODULE_OBJS))
#$(info MODULE_COBJS = $(MODULE_COBJS))
#$(info MODULE_CPPOBJS = $(MODULE_CPPOBJS))
#$(info MODULE_ASMOBJS = $(MODULE_ASMOBJS))

#$(info MODULE_INPLACE_SRCS = $(MODULE_INPLACE_SRCS))
#$(info MODULE_INPLACE_CSRCS = $(MODULE_INPLACE_CSRCS))
#$(info MODULE_INPLACE_CPPSRCS = $(MODULE_INPLACE_CPPSRCS))
#$(info MODULE_INPLACE_ASMSRCS = $(MODULE_INPLACE_ASMSRCS))

#$(info MODULE_INPLACE_OBJS = $(MODULE_INPLACE_OBJS))
#$(info MODULE_INPLACE_COBJS = $(MODULE_INPLACE_COBJS))
#$(info MODULE_INPLACE_CPPOBJS = $(MODULE_INPLACE_CPPOBJS))
#$(info MODULE_INPLACE_ASMOBJS = $(MODULE_INPLACE_ASMOBJS))

$(MODULE_INIT_OBJS): MODULE_OPTFLAGS:=$(MODULE_OPTFLAGS)
$(MODULE_INIT_OBJS): MODULE_COMPILEFLAGS:=$(MODULE_COMPILEFLAGS)
$(MODULE_INIT_OBJS): MODULE_CFLAGS:=$(MODULE_CFLAGS)
$(MODULE_INIT_OBJS): MODULE_CPPFLAGS:=$(MODULE_CPPFLAGS)
$(MODULE_INIT_OBJS): MODULE_ASMFLAGS:=$(MODULE_ASMFLAGS)
$(MODULE_INIT_OBJS): MODULE_SRCDEPS:=$(MODULE_SRCDEPS)
$(MODULE_INIT_OBJS): MODULE_INCLUDES:=$(MODULE_INCLUDES)

$(MODULE_OBJS): MODULE_OPTFLAGS:=$(MODULE_OPTFLAGS)
$(MODULE_OBJS): MODULE_COMPILEFLAGS:=$(MODULE_COMPILEFLAGS)
$(MODULE_OBJS): MODULE_CFLAGS:=$(MODULE_CFLAGS)
$(MODULE_OBJS): MODULE_CPPFLAGS:=$(MODULE_CPPFLAGS)
$(MODULE_OBJS): MODULE_ASMFLAGS:=$(MODULE_ASMFLAGS)
$(MODULE_OBJS): MODULE_SRCDEPS:=$(MODULE_SRCDEPS)
$(MODULE_OBJS): MODULE_INCLUDES:=$(MODULE_INCLUDES)

$(MODULE_COBJS): $(BUILDDIR)/%.o: %.c $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CFLAGS) $(ARCH_CFLAGS) $(MODULE_CFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_CPPOBJS): $(BUILDDIR)/%.o: %.cpp $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CPPFLAGS) $(ARCH_CPPFLAGS) $(MODULE_CPPFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_CCOBJS): $(BUILDDIR)/%.o: %.cc $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CPPFLAGS) $(ARCH_CPPFLAGS) $(MODULE_CPPFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_ASMOBJS): $(BUILDDIR)/%.o: %.S $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_ASMFLAGS) $(ARCH_ASMFLAGS) $(MODULE_ASMFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

# Same rules as normal sources but output file is %.o rather then $(BUILDDIR)/%.o
$(MODULE_INPLACE_COBJS): %.o : %.c $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CFLAGS) $(ARCH_CFLAGS) $(MODULE_CFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_INPLACE_CPPOBJS): %.o : %.cpp $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CPPFLAGS) $(ARCH_CPPFLAGS) $(MODULE_CPPFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_INPLACE_CCOBJS): %.o : %.cc $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CPPFLAGS) $(ARCH_CPPFLAGS) $(MODULE_CPPFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_INPLACE_ASMOBJS): %.o : %.S $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_ASMFLAGS) $(ARCH_ASMFLAGS) $(MODULE_ASMFLAGS) $(THUMBCFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

# overridden arm versions
$(MODULE_ARM_COBJS): $(BUILDDIR)/%.o: %.c $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CFLAGS) $(ARCH_CFLAGS) $(MODULE_CFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_ARM_CPPOBJS): $(BUILDDIR)/%.o: %.cpp $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CPPFLAGS) $(ARCH_CPPFLAGS) $(MODULE_CPPFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_ARM_CCOBJS): $(BUILDDIR)/%.o: %.cc $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_CPPFLAGS) $(ARCH_CPPFLAGS) $(MODULE_CPPFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

$(MODULE_ARM_ASMOBJS): $(BUILDDIR)/%.o: %.S $(MODULE_SRCDEPS)
	@$(MKDIR)
	@echo compiling $<
	$(NOECHO)$(CC) $(GLOBAL_OPTFLAGS) $(MODULE_OPTFLAGS) $(GLOBAL_COMPILEFLAGS) $(ARCH_COMPILEFLAGS) $(MODULE_COMPILEFLAGS) $(GLOBAL_ASMFLAGS) $(ARCH_ASMFLAGS) $(MODULE_ASMFLAGS) $(MODULE_INCLUDES) $(GLOBAL_INCLUDES) -c $< -MD -MP -MT $@ -MF $(@:%o=%d) -o $@

# clear some variables we set here
MODULE_CSRCS :=
MODULE_CPPSRCS :=
MODULE_CCSRCS :=
MODULE_ASMSRCS :=
MODULE_COBJS :=
MODULE_CPPOBJS :=
MODULE_CCOBJS :=
MODULE_ASMOBJS :=

MODULE_INPLACE_CSRCS :=
MODULE_INPLACE_CPPSRCS :=
MODULE_INPLACE_CCSRCS :=
MODULE_INPLACE_ASMSRCS :=
MODULE_INPLACE_COBJS :=
MODULE_INPLACE_CPPOBJS :=
MODULE_INPLACE_CCOBJS :=
MODULE_INPLACE_ASMOBJS :=

MODULE_INPLACE_SRCS:=
MODULE_OUTPLACE_SRCS:=

MODULE_INPLACE_SRCS_FIRST:=
MODULE_OUTPLACE_SRCS_FIRST:=

MODULE_CSRCS_FIRST :=
MODULE_CPPSRCS_FIRST :=
MODULE_CCSRCS_FIRST :=
MODULE_ASMSRCS_FIRST :=

MODULE_COBJS_FIRST :=
MODULE_CPPOBJS_FIRST :=
MODULE_CCOBJS_FIRST :=
MODULE_ASMOBJS_FIRST :=

MODULE_INPLACE_CSRCS_FIRST :=
MODULE_INPLACE_CPPSRCS_FIRST :=
MODULE_INPLACE_CCSRCS_FIRST :=
MODULE_INPLACE_ASMSRCS_FIRST :=
MODULE_INPLACE_COBJS_FIRST :=
MODULE_INPLACE_CPPOBJS_FIRST :=
MODULE_INPLACE_CCOBJS_FIRST :=
MODULE_INPLACE_ASMOBJS_FIRST :=

# MODULE_OBJS is passed back
#MODULE_OBJS :=

