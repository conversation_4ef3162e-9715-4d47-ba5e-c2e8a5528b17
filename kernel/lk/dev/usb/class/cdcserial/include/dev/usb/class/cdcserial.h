/*
 * Copyright 2016 Google Inc. All Rights Reserved.
 * Author: g<PERSON><PERSON>@google.com (<PERSON><PERSON><PERSON><PERSON>)
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files
 * (the "Software"), to deal in the Software without restriction,
 * including without limitation the rights to use, copy, modify, merge,
 * publish, distribute, sublicense, and/or sell copies of the Software,
 * and to permit persons to whom the Software is furnished to do so,
 * subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
 * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
 * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
 * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
 * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */

#pragma once

#include <sys/types.h>

status_t cdcserial_init(void);

void cdcserial_create_channel(int data_ep_addr, int ctrl_ep_addr);

// Write len bytes to the CDC Serial Virtual Com Port. 
status_t cdcserial_write(size_t len, uint8_t *buf);

// Read at most len bytes from the CDC Serial virtual Com Port. Returns the 
// actual number of bytes read.
ssize_t cdcserial_read(size_t len, uint8_t *buf);