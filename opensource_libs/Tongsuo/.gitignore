# Ignore editor artefacts
/.dir-locals.el

# Top level excludes
/Makefile.in
/Makefile
/MINFO
/TABLE
/*.pc
/rehash.time
/inc.*
/makefile.*
/out.*
/tmp.*
/configdata.pm

# Links under apps
/apps/CA.pl
/apps/tsget
/apps/tsget.pl

# Auto generated headers
/crypto/buildinf.h
/include/crypto/*_conf.h
/include/openssl/asn1.h
/include/openssl/asn1t.h
/include/openssl/bio.h
/include/openssl/cmp.h
/include/openssl/cms.h
/include/openssl/conf.h
/include/openssl/configuration.h
/include/openssl/crmf.h
/include/openssl/crypto.h
/include/openssl/ct.h
/include/openssl/err.h
/include/openssl/ess.h
/include/openssl/fipskey.h
/include/openssl/lhash.h
/include/openssl/ocsp.h
/include/openssl/opensslv.h
/include/openssl/pkcs12.h
/include/openssl/pkcs7.h
/include/openssl/safestack.h
/include/openssl/srp.h
/include/openssl/ssl.h
/include/openssl/ui.h
/include/openssl/x509.h
/include/openssl/x509v3.h
/include/openssl/x509_vfy.h

# Auto generated doc files
doc/man1/openssl-*.pod

# Auto generated der files
providers/common/der/der_digests_gen.c
providers/common/der/der_dsa_gen.c
providers/common/der/der_ec_gen.c
providers/common/der/der_ecx_gen.c
providers/common/der/der_rsa_gen.c
providers/common/der/der_wrap_gen.c
providers/common/der/der_sm2_gen.c
providers/common/include/prov/der_dsa.h
providers/common/include/prov/der_ec.h
providers/common/include/prov/der_ecx.h
providers/common/include/prov/der_rsa.h
providers/common/include/prov/der_digests.h
providers/common/include/prov/der_wrap.h
providers/common/include/prov/der_sm2.h

providers/smtc/smtckey.h

# error code files
/crypto/err/openssl.txt.old
/engines/e_afalg.txt.old
/engines/e_capi.txt.old
/engines/e_dasync.txt.old
/engines/e_ossltest.txt.old

# Executables
/apps/openssl
/test/sha256t
/test/sha512t
/test/gost2814789t
/test/ssltest_old
/test/*test
/test/fips_aesavs
/test/fips_desmovs
/test/fips_dhvs
/test/fips_drbgvs
/test/fips_dssvs
/test/fips_ecdhvs
/test/fips_ecdsavs
/test/fips_rngvs
/test/fips_test_suite
/test/ssltest_old
/test/x509aux
/test/v3ext
/test/versions
/test/ossl_shim/ossl_shim
/test/rsa_complex
/test/confdump
/test/bio_prefix_text
/test/evp_extra_test2
/test/threadstest_fips

# Certain files that get created by tests on the fly
/test-runs
/test/buildtest_*
/test/provider_internal_test.cnf
/test/fipsmodule.cnf
/test/smtcmodule.cnf
/providers/fipsmodule.cnf

# Fuzz stuff.
# Anything without an extension is an executable on Unix, so we keep files
# with extensions.  And we keep the corpora subddir versioned as well.
# Anything more generic with extensions that should be ignored will be taken
# care of by general ignores for those extensions (*.o, *.obj, *.exe, ...)
/fuzz/*
!/fuzz/README*
!/fuzz/corpora
!/fuzz/*.*

# Misc auto generated files
/doc/man7/openssl_user_macros.pod
/tools/c_rehash
/tools/c_rehash.pl
/util/shlib_wrap.sh
/util/wrap.pl
/tags
/TAGS
*.map
*.ld
/apps/progs.c
/apps/progs.h

# Windows (legacy)
/tmp32
/tmp32.dbg
/tmp32dll
/tmp32dll.dbg
/out32
/out32.dbg
/out32dll
/out32dll.dbg
/inc32
/MINFO
/ms/bcb.mak
/ms/libeay32.def
/ms/nt.mak
/ms/ntdll.mak
/ms/ssleay32.def
/ms/version32.rc

# Files created on other branches that are not held in git, and are not
# needed on this branch
/include/openssl/symbol_prefix.h
/include/openssl/asn1_mac.h
/include/openssl/des_old.h
/include/openssl/fips.h
/include/openssl/fips_rand.h
/include/openssl/krb5_asn.h
/include/openssl/kssl.h
/include/openssl/pq_compat.h
/include/openssl/ssl23.h
/include/openssl/tmdiff.h
/include/openssl/ui_compat.h
/test/fips_aesavs.c
/test/fips_desmovs.c
/test/fips_dsatest.c
/test/fips_dssvs.c
/test/fips_hmactest.c
/test/fips_randtest.c
/test/fips_rngvs.c
/test/fips_rsagtest.c
/test/fips_rsastest.c
/test/fips_rsavtest.c
/test/fips_shatest.c
/test/fips_test_suite.c
/test/shatest.c

# Generated docs directories
/doc/html
/doc/man

##### Generic patterns
# Auto generated assembly language source files
*.s
!/crypto/*/asm/*.s
/crypto/arm*.S
/crypto/*/*.S
*.asm
!/crypto/*/asm/*.asm

# Object files
*.o
*.obj

# Mac debug symbols files
*.dSYM

# editor artefacts
*.swp
.#*
\#*#
*~

# Certificate symbolic links
*.0

# All kinds of libraries and executables
*.a
*.so
*.so.*
*.dylib
*.dylib.*
*.dll
*.dll.*
*.exe
*.pyc
*.exp
*.lib
*.pdb
*.tds
*.ilk
*.def
*.rc
*.res

# Misc generated stuff
Makefile.save
/crypto/**/lib
/engines/**/lib
/ssl/**/lib
*.bak
cscope.*
*.d
*.d.tmp
pod2htmd.tmp
MAKE0[0-9][0-9][0-9].@@@

# Windows manifest files
*.manifest
doc-nits

# Mac .dSYM files
*.dSYM
./build-arm64/*