/*
 * WARNING: do not edit!
 * Generated by util/mkbuildinf.pl
 *
 * Copyright 2014-2017 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#define PLATFORM "platform: linux-aarch64"
#define DATE "built on: Tue Jul  8 07:51:33 2025 UTC"

/*
 * Generate compiler_flags as an array of individual characters. This is a
 * workaround for the situation where CFLAGS gets too long for a C90 string
 * literal
 */
static const char compiler_flags[] = {
    'c','o','m','p','i','l','e','r',':',' ','/','h','o','m','e','/',
    'g','o','n','g','y','f','/','c','o','d','e','b','a','s','e','/',
    't','r','u','s','t','y','/','p','r','e','b','u','i','l','t','s',
    '/','c','l','a','n','g','/','h','o','s','t','/','l','i','n','u',
    'x','-','x','8','6','/','c','l','a','n','g','-','r','4','7','5',
    '3','6','5','b','/','b','i','n','/','c','l','a','n','g',' ','-',
    'f','P','I','C',' ','-','W','a',',','-','-','n','o','e','x','e',
    'c','s','t','a','c','k',' ','-','Q','u','n','u','s','e','d','-',
    'a','r','g','u','m','e','n','t','s',' ','-','t','a','r','g','e',
    't',' ','a','a','r','c','h','6','4','-','l','i','n','u','x','-',
    'a','n','d','r','o','i','d',' ','-','m','a','r','c','h','=','a',
    'r','m','v','8','-','a',' ','-','f','P','I','C',' ','-','O','s',
    ' ','-','f','f','u','n','c','t','i','o','n','-','s','e','c','t',
    'i','o','n','s',' ','-','f','d','a','t','a','-','s','e','c','t',
    'i','o','n','s',' ','-','W','n','o','-','a','r','r','a','y','-',
    'b','o','u','n','d','s',' ','-','W','n','o','-','e','r','r','o',
    'r',' ','-','D','O','P','E','N','S','S','L','_','U','S','E','_',
    'N','O','D','E','L','E','T','E',' ','-','D','O','P','E','N','S',
    'S','L','_','P','I','C',' ','-','D','O','P','E','N','S','S','L',
    '_','B','U','I','L','D','I','N','G','_','O','P','E','N','S','S',
    'L',' ','-','D','N','D','E','B','U','G',' ','-','D','O','P','E',
    'N','S','S','L','_','N','O','_','S','T','D','I','O',' ','-','D',
    'O','P','E','N','S','S','L','_','N','O','_','S','O','C','K',' ',
    '-','D','O','P','E','N','S','S','L','_','N','O','_','T','H','R',
    'E','A','D','S',' ','-','D','_','_','T','E','E','_','_',' ','\0'
};
