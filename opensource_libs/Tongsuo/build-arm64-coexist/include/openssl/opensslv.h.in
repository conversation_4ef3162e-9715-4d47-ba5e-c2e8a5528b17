/*
 * {- join("\n * ", @autowarntext) -}
 *
 * Copyright 1999-2020 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#ifndef OPENSSL_OPENSSLV_H
# define OPENSSL_OPENSSLV_H
# pragma once

# ifdef  __cplusplus
extern "C" {
# endif

/*
 * SECTION 1: VERSION DATA.  These will change for each release
 */

/*
 * Base version macros
 *
 * These macros express version number MAJOR.MINOR.PATCH exactly
 */
# define OPENSSL_VERSION_MAJOR  {- $config{major} -}
# define OPENSSL_VERSION_MINOR  {- $config{minor} -}
# define OPENSSL_VERSION_PATCH  {- $config{patch} -}

# define TONGSUO_VERSION_MAJOR  {- $config{tongsuo_major} -}
# define TONGSUO_VERSION_MINOR  {- $config{tongsuo_minor} -}
# define TONGSUO_VERSION_PATCH  {- $config{tongsuo_patch} -}

/*
 * Additional version information
 *
 * These are also part of the new version scheme, but aren't part
 * of the version number itself.
 */

/* Could be: #define OPENSSL_VERSION_PRE_RELEASE "-alpha.1" */
# define OPENSSL_VERSION_PRE_RELEASE "{- $config{prerelease} -}"
/* Could be: #define OPENSSL_VERSION_BUILD_METADATA "+fips" */
/* Could be: #define OPENSSL_VERSION_BUILD_METADATA "+vendor.1" */
# define OPENSSL_VERSION_BUILD_METADATA "{- $config{build_metadata} -}"

# define TONGSUO_VERSION_PRE_RELEASE "{- $config{tongsuo_prerelease} -}"

/*
 * Note: The OpenSSL Project will never define OPENSSL_VERSION_BUILD_METADATA
 * to be anything but the empty string.  Its use is entirely reserved for
 * others
 */

/*
 * Shared library version
 *
 * This is strictly to express ABI version, which may or may not
 * be related to the API version expressed with the macros above.
 * This is defined in free form.
 */
# define OPENSSL_SHLIB_VERSION {- $config{shlib_version} -}

/*
 * SECTION 2: USEFUL MACROS
 */

/* For checking general API compatibility when preprocessing */
# define OPENSSL_VERSION_PREREQ(maj,min)                                \
    ((OPENSSL_VERSION_MAJOR << 16) + OPENSSL_VERSION_MINOR >= ((maj) << 16) + (min))

/*
 * Macros to get the version in easily digested string form, both the short
 * "MAJOR.MINOR.PATCH" variant (where MAJOR, MINOR and PATCH are replaced
 * with the values from the corresponding OPENSSL_VERSION_ macros) and the
 * longer variant with OPENSSL_VERSION_PRE_RELEASE_STR and
 * OPENSSL_VERSION_BUILD_METADATA_STR appended.
 */
# define OPENSSL_VERSION_STR "{- $config{version} -}"
# define OPENSSL_FULL_VERSION_STR "{- $config{full_version} -}"

/*
 * SECTION 3: ADDITIONAL METADATA
 *
 * These strings are defined separately to allow them to be parsable.
 */
# define OPENSSL_RELEASE_DATE "{- $config{release_date} -}"

/*
 * SECTION 4: BACKWARD COMPATIBILITY
 */

# define OPENSSL_VERSION_TEXT "OpenSSL {- "$config{full_version} $config{release_date}" -}"

/* Synthesize OPENSSL_VERSION_NUMBER with the layout 0xMNN00PPSL */
# ifdef OPENSSL_VERSION_PRE_RELEASE
#  define _OPENSSL_VERSION_PRE_RELEASE 0x0L
# else
#  define _OPENSSL_VERSION_PRE_RELEASE 0xfL
# endif
# define OPENSSL_VERSION_NUMBER          \
    ( (OPENSSL_VERSION_MAJOR<<28)        \
      |(OPENSSL_VERSION_MINOR<<20)       \
      |(OPENSSL_VERSION_PATCH<<4)        \
      |_OPENSSL_VERSION_PRE_RELEASE )

/* Tongsuo/BabaSSL stuffs */
# ifdef TONGSUO_VERSION_PRE_RELEASE
#  define _TONGSUO_VERSION_PRE_RELEASE 0x0L
# else
#  define _TONGSUO_VERSION_PRE_RELEASE 0xfL
# endif
# define TONGSUO_VERSION_NUMBER          \
    ( (TONGSUO_VERSION_MAJOR<<28)        \
      |(TONGSUO_VERSION_MINOR<<20)       \
      |(TONGSUO_VERSION_PATCH<<4)        \
      |_TONGSUO_VERSION_PRE_RELEASE )

# define TONGSUO_VERSION_STR "{- $config{tongsuo_version} -}"
# define TONGSUO_FULL_VERSION_STR "{- $config{tongsuo_full_version} -}"

# define TONGSUO_VERSION_TEXT    "Tongsuo {- "$config{tongsuo_full_version}" -}"
# define BABASSL_VERSION_NUMBER  TONGSUO_VERSION_NUMBER
# define BABASSL_VERSION_TEXT    TONGSUO_VERSION_TEXT

# define TONGSUO_SMTC_INFO_STR "{- $config{tongsuo_smtc_info} -}"

# ifdef  __cplusplus
}
# endif

# include <openssl/macros.h>
# ifndef OPENSSL_NO_DEPRECATED_3_0
#  define HEADER_OPENSSLV_H
# endif

#endif                          /* OPENSSL_OPENSSLV_H */
