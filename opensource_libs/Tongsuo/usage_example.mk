# 铜锁库使用示例 Makefile
# 展示如何在rctee项目中使用编译好的ARM铜锁库

# 铜锁库路径
TONGSUO_ROOT := $(CURDIR)
TONGSUO_BUILD := $(TONGSUO_ROOT)/build-arm64
TONGSUO_INCLUDE := $(TONGSUO_BUILD)/include
TONGSUO_LIB := $(TONGSUO_BUILD)/lib

# 编译器设置 (使用相同的工具链)
COMPILER_PATH := /home/<USER>/codebase/trusty/prebuilts
CC := $(COMPILER_PATH)/clang/host/linux-x86/clang-r475365b/bin/clang
AR := $(COMPILER_PATH)/clang/host/linux-x86/clang-r475365b/bin/llvm-ar

# 编译标志
CFLAGS := -target aarch64-linux-android -march=armv8-a
CFLAGS += -fPIC -Os -ffunction-sections -fdata-sections
CFLAGS += -I$(TONGSUO_INCLUDE)
CFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS -D__TEE__

# 链接标志
LDFLAGS := -L$(TONGSUO_LIB)
LIBS := -lcrypto -lssl

# 示例：如何在你的项目中使用
# 在你的模块Makefile中添加：

# 包含铜锁头文件
MODULE_INCLUDES += $(TONGSUO_INCLUDE)

# 链接铜锁库
MODULE_LIBRARY_DEPS += $(TONGSUO_LIB)/libcrypto.a
MODULE_LIBRARY_DEPS += $(TONGSUO_LIB)/libssl.a

# 添加编译定义
MODULE_COMPILEFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS -D__TEE__

# 示例代码编译
example: example.c
	$(CC) $(CFLAGS) -o $@ $< $(LDFLAGS) $(LIBS)

# 清理
clean:
	rm -f example

.PHONY: clean example

# 使用说明：
# 1. 在你的C代码中包含需要的头文件：
#    #include <openssl/evp.h>
#    #include <openssl/sm2.h>
#    #include <openssl/sm3.h>
#    #include <openssl/sm4.h>
#
# 2. 使用EVP接口调用算法：
#    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
#    EVP_DigestInit_ex(ctx, EVP_sm3(), NULL);
#    // ... 使用SM3摘要算法
#
# 3. 编译时链接铜锁库：
#    make example
