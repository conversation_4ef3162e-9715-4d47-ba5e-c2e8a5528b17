# Copyright (C) 2024 rctee project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Tongsuo build configuration for rctee TEE system

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# Tongsuo configuration for TEE environment
MODULE_COMPILEFLAGS += -D__linux__ -D__TRUSTY__ -D__TEE__
MODULE_COMPILEFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK
MODULE_COMPILEFLAGS += -DOPENSSL_NO_DGRAM -DOPENSSL_NO_ASYNC
MODULE_COMPILEFLAGS += -DOPENSSL_NO_DEPRECATED -DOPENSSL_NO_ENGINE
MODULE_COMPILEFLAGS += -DOPENSSL_NO_AUTOLOAD_CONFIG

# Enable SM algorithms for Chinese cryptography
MODULE_COMPILEFLAGS += -DOPENSSL_ENABLE_SM2 -DOPENSSL_ENABLE_SM3 -DOPENSSL_ENABLE_SM4
MODULE_COMPILEFLAGS += -DOPENSSL_ENABLE_NTLS

# TEE specific optimizations
MODULE_COMPILEFLAGS += -Os -ffunction-sections -fdata-sections
MODULE_COMPILEFLAGS += -fno-exceptions -fno-rtti

# Include directories
MODULE_INCLUDES += $(LOCAL_DIR)/include
MODULE_INCLUDES += $(LOCAL_DIR)/crypto/include

# Define static armcap based on lk build variables
MODULE_STATIC_ARMCAP := -DOPENSSL_STATIC_ARMCAP
toarmcap = $(if $(filter-out 0 false,$(2)),-DOPENSSL_STATIC_ARMCAP_$(1),)
MODULE_STATIC_ARMCAP += $(call toarmcap,NEON,$(USE_ARM_V7_NEON))
MODULE_STATIC_ARMCAP += $(call toarmcap,AES,$(USE_ARM_V8_AES))
MODULE_STATIC_ARMCAP += $(call toarmcap,PMULL,$(USE_ARM_V8_PMULL))
MODULE_STATIC_ARMCAP += $(call toarmcap,SHA1,$(USE_ARM_V8_SHA1))
MODULE_STATIC_ARMCAP += $(call toarmcap,SHA256,$(USE_ARM_V8_SHA256))
MODULE_STATIC_ARMCAP += $(call toarmcap,SHA512,$(USE_ARM_V8_SHA512))

MODULE_COMPILEFLAGS += $(MODULE_STATIC_ARMCAP)

# Source files will be added by tongsuo-sources.mk
MODULE_SRCDEPS += $(LOCAL_DIR)/tongsuo-sources.mk
include $(LOCAL_DIR)/tongsuo-sources.mk

include make/library.mk
