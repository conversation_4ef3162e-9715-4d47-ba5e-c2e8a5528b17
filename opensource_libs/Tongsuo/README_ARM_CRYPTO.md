# 铜锁ARM加密库编译成功 (带符号前缀)

## 编译结果

✅ **成功编译出ARM64架构的铜锁加密库，支持与OpenSSL共存**

- **库文件**: `build-arm64/lib/libcrypto.a` (9.8MB)
- **头文件**: `build-arm64/include/openssl/`
- **符号前缀**: `TONGSUO_` (支持与其他OpenSSL版本共存)
- **目标架构**: ARM64 (aarch64)
- **工具链**: `/home/<USER>/codebase/trusty/prebuilts/clang/host/linux-x86/clang-r475365b`

## 特性

### ✅ 只包含加密库 + 符号前缀
- **仅安装**: libcrypto.a (加密库)
- **不安装**: libssl.a (SSL/TLS库，虽然被编译但不安装)
- **符号前缀**: 所有符号都有 `TONGSUO_` 前缀
- **共存能力**: 可与其他OpenSSL版本共存
- **适用于**: TEE环境，只需要加密功能

### ✅ 支持的算法
根据您的需求，编译的库支持以下算法：

#### 1. 摘要计算
- MD5, SHA-1, SHA-224, SHA-256, SHA-384, SHA-512
- SM3-256 ✅
- SHA3-224, SHA3-256, SHA3-384, SHA3-512
- SHAKE128, SHAKE256

#### 2. 对称加密
- DES, 3DES (双倍/三倍密钥)
- AES (所有模式)
- SM4 ✅ (所有模式)

#### 3. 消息认证码
- DES-MAC, AES-MAC, AES-CMAC
- HMAC (支持所有摘要算法)

#### 4. 认证加密
- AES-CCM (支持AAD)
- AES-GCM (支持AAD)

#### 5. 非对称加密
- RSA PKCS1-V1.5, RSA OAEP

#### 6. 数字签名
- DSA, RSA PKCS1-V1.5, RSA PSS

#### 7. 密钥交换
- Diffie-Hellman

#### 8. 国密算法 ✅
- SM2-DSA, SM2-KEP, SM2-PKE
- SM3, SM4

#### 9. 椭圆曲线算法
- ECDSA, ECDH

## 编译配置

### 启用的特性
```bash
enable-sm2 enable-sm3 enable-sm4  # 国密算法
--symbol-prefix=TONGSUO_          # 符号前缀，支持共存
```

### 禁用的特性
```bash
no-shared no-dso no-engine        # 静态库，无动态加载
no-async no-sock no-dgram         # 无异步、网络功能
no-stdio no-threads               # 无标准IO、线程
no-autoload-config no-deprecated  # 无自动配置、废弃功能
no-tests no-ui-console            # 无测试、控制台UI
no-ssl no-tls no-dtls             # 禁用SSL/TLS相关功能
no-ssl3 no-tls1* no-dtls*         # 禁用具体SSL/TLS版本
```

### TEE优化
```bash
-DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS -D__TEE__
```

## 使用方法

### 在项目中使用
```makefile
# 包含头文件
INCLUDES += -I$(TONGSUO_ROOT)/build-arm64/include

# 链接库文件
LIBS += $(TONGSUO_ROOT)/build-arm64/lib/libcrypto.a

# 编译定义
CFLAGS += -DOPENSSL_NO_STDIO -DOPENSSL_NO_SOCK -DOPENSSL_NO_THREADS -D__TEE__
```

### 代码示例
```c
#include <openssl/evp.h>  // 自动包含symbol_prefix.h
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>

// 使用SM3摘要 - 符号自动映射到TONGSUO_前缀
EVP_MD_CTX *ctx = EVP_MD_CTX_new();        // 实际调用TONGSUO_EVP_MD_CTX_new
EVP_DigestInit_ex(ctx, EVP_sm3(), NULL);   // 实际调用TONGSUO_EVP_sm3
// ... 使用SM3算法

// 使用SM4加密 - 符号自动映射
EVP_CIPHER_CTX *cipher_ctx = EVP_CIPHER_CTX_new();  // 实际调用TONGSUO_EVP_CIPHER_CTX_new
EVP_EncryptInit_ex(cipher_ctx, EVP_sm4_cbc(), NULL, key, iv);  // 实际调用TONGSUO_EVP_sm4_cbc
// ... 使用SM4算法

// 代码完全不需要修改，符号前缀自动处理！
```

## 重新编译

如需重新编译，运行：
```bash
cd opensource_libs/Tongsuo
./build_tongsuo_arm.sh
```

## 验证

库已验证包含所需的算法符号（带TONGSUO_前缀）：
- SM2: `TONGSUO_EVP_PKEY_SM2`, `TONGSUO_ossl_sm2_*`
- SM3: `TONGSUO_EVP_sm3`, `TONGSUO_SM3_*`
- SM4: `TONGSUO_EVP_sm4_cbc`, `TONGSUO_EVP_sm4_ccm`, `TONGSUO_EVP_sm4_gcm` 等

### 符号前缀验证
```bash
# 检查符号前缀
nm build-arm64/lib/libcrypto.a | grep "TONGSUO_EVP_sm" | head -5

# 输出示例:
# U TONGSUO_EVP_sm4_cbc
# U TONGSUO_EVP_sm4_ccm
# U TONGSUO_EVP_sm4_cfb128
# U TONGSUO_EVP_sm4_ctr
# U TONGSUO_EVP_sm4_ecb
```

**编译完成时间**: 2024年7月8日
**状态**: ✅ 成功，支持OpenSSL共存，可用于rctee项目
