#!/bin/bash

# 检查铜锁库中支持的算法
# 用于验证编译出的ARM库是否包含所需的算法

LIB_PATH="build-arm64/lib/libcrypto.a"

echo "=== 铜锁ARM库算法支持检查 ==="
echo "库文件: $LIB_PATH"
echo ""

if [ ! -f "$LIB_PATH" ]; then
    echo "错误: 库文件不存在"
    exit 1
fi

echo "1. 摘要算法检查:"
echo "  MD5:"
nm $LIB_PATH | grep -i md5 | grep -E "(MD5_|EVP_md5)" | head -3
echo "  SHA-1:"
nm $LIB_PATH | grep -i sha1 | grep -E "(SHA1_|EVP_sha1)" | head -3
echo "  SHA-256:"
nm $LIB_PATH | grep -i sha256 | grep -E "(SHA256_|EVP_sha256)" | head -3
echo "  SHA-512:"
nm $LIB_PATH | grep -i sha512 | grep -E "(SHA512_|EVP_sha512)" | head -3
echo "  SM3:"
nm $LIB_PATH | grep -i sm3 | grep -E "(SM3_|EVP_sm3)" | head -3
echo "  SHA3:"
nm $LIB_PATH | grep -i sha3 | grep -E "(SHA3_|EVP_sha3)" | head -3
echo ""

echo "2. 对称加密算法检查:"
echo "  DES:"
nm $LIB_PATH | grep -i des | grep -E "(DES_|EVP_des)" | head -3
echo "  3DES:"
nm $LIB_PATH | grep -i des | grep -E "(des_ede|EVP_des_ede)" | head -3
echo "  AES:"
nm $LIB_PATH | grep -i aes | grep -E "(AES_|EVP_aes)" | head -3
echo "  SM4:"
nm $LIB_PATH | grep -i sm4 | grep -E "(SM4_|EVP_sm4)" | head -3
echo ""

echo "3. 消息认证码检查:"
echo "  HMAC:"
nm $LIB_PATH | grep -i hmac | grep -E "(HMAC_|EVP_)" | head -3
echo "  CMAC:"
nm $LIB_PATH | grep -i cmac | grep -E "(CMAC_|EVP_)" | head -3
echo ""

echo "4. 认证加密检查:"
echo "  AES-CCM:"
nm $LIB_PATH | grep -i ccm | grep -E "(CCM|EVP_aes.*ccm)" | head -3
echo "  AES-GCM:"
nm $LIB_PATH | grep -i gcm | grep -E "(GCM|EVP_aes.*gcm)" | head -3
echo ""

echo "5. 非对称加密检查:"
echo "  RSA:"
nm $LIB_PATH | grep -i rsa | grep -E "(RSA_|EVP_PKEY_RSA)" | head -3
echo ""

echo "6. 数字签名检查:"
echo "  DSA:"
nm $LIB_PATH | grep -i dsa | grep -E "(DSA_|EVP_PKEY_DSA)" | head -3
echo "  RSA签名:"
nm $LIB_PATH | grep -i rsa | grep -E "(sign|verify)" | head -3
echo ""

echo "7. 密钥交换检查:"
echo "  Diffie-Hellman:"
nm $LIB_PATH | grep -i dh | grep -E "(DH_|EVP_PKEY_DH)" | head -3
echo ""

echo "8. 国密算法检查:"
echo "  SM2-DSA:"
nm $LIB_PATH | grep -i sm2 | grep -E "(SM2_|EVP_PKEY_SM2)" | head -3
echo "  SM2-KEP:"
nm $LIB_PATH | grep -i sm2 | grep -E "(kep|KEP)" | head -3
echo "  SM2-PKE:"
nm $LIB_PATH | grep -i sm2 | grep -E "(encrypt|decrypt)" | head -3
echo ""

echo "9. 椭圆曲线算法检查:"
echo "  ECDSA:"
nm $LIB_PATH | grep -i ecdsa | grep -E "(ECDSA_|EVP_)" | head -3
echo "  ECDH:"
nm $LIB_PATH | grep -i ecdh | grep -E "(ECDH_|EVP_)" | head -3
echo ""

echo "=== 库信息 ==="
echo "库大小:"
ls -lh $LIB_PATH
echo ""
echo "库架构信息:"
file $LIB_PATH
echo ""

echo "=== 头文件检查 ==="
echo "主要头文件:"
ls -la build-arm64/include/openssl/ | grep -E "(evp|sm|aes|des|rsa|dsa|ec|dh)\.h"
echo ""

echo "检查完成!"
