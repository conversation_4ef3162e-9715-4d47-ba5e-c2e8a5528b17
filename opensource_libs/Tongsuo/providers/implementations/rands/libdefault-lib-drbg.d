providers/implementations/rands/libdefault-lib-drbg.o: \
  providers/implementations/rands/drbg.c include/openssl/crypto.h \
  include/openssl/macros.h include/openssl/opensslconf.h \
  include/openssl/configuration.h include/openssl/symbol_prefix.h \
  include/openssl/opensslv.h include/openssl/e_os2.h \
  include/openssl/safestack.h include/openssl/stack.h \
  include/openssl/types.h include/openssl/cryptoerr.h \
  include/openssl/symhacks.h include/openssl/cryptoerr_legacy.h \
  include/openssl/core.h include/openssl/err.h include/openssl/bio.h \
  include/openssl/bioerr.h include/openssl/lhash.h \
  include/openssl/rand.h include/openssl/randerr.h include/openssl/evp.h \
  include/openssl/core_dispatch.h include/openssl/evperr.h \
  include/openssl/params.h include/openssl/bn.h include/openssl/bnerr.h \
  include/openssl/objects.h include/openssl/obj_mac.h \
  include/openssl/asn1.h include/openssl/asn1err.h \
  include/openssl/objectserr.h include/crypto/rand.h \
  include/crypto/rand_pool.h include/openssl/proverr.h \
  providers/implementations/rands/drbg_local.h \
  include/openssl/core_names.h include/internal/tsan_assist.h \
  include/internal/nelem.h include/internal/numbers.h \
  providers/common/include/prov/provider_ctx.h \
  providers/common/include/prov/provider_util.h \
  include/openssl/provider.h include/internal/thread_once.h \
  include/crypto/cryptlib.h include/internal/cryptlib.h \
  include/openssl/buffer.h include/openssl/buffererr.h \
  providers/implementations/include/prov/seeding.h \
  providers/common/include/prov/providercommon.h
