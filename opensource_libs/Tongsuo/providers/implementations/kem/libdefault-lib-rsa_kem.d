providers/implementations/kem/libdefault-lib-rsa_kem.o: \
  providers/implementations/kem/rsa_kem.c include/internal/deprecated.h \
  include/openssl/configuration.h include/openssl/macros.h \
  include/openssl/opensslconf.h include/openssl/symbol_prefix.h \
  include/openssl/opensslv.h include/internal/nelem.h \
  include/openssl/crypto.h include/openssl/e_os2.h \
  include/openssl/safestack.h include/openssl/stack.h \
  include/openssl/types.h include/openssl/cryptoerr.h \
  include/openssl/symhacks.h include/openssl/cryptoerr_legacy.h \
  include/openssl/core.h include/openssl/evp.h \
  include/openssl/core_dispatch.h include/openssl/bio.h \
  include/openssl/bioerr.h include/openssl/evperr.h \
  include/openssl/params.h include/openssl/bn.h include/openssl/bnerr.h \
  include/openssl/objects.h include/openssl/obj_mac.h \
  include/openssl/asn1.h include/openssl/asn1err.h \
  include/openssl/objectserr.h include/openssl/core_names.h \
  include/openssl/rsa.h include/openssl/rsaerr.h include/openssl/err.h \
  include/openssl/lhash.h include/crypto/rsa.h include/crypto/types.h \
  include/openssl/proverr.h providers/common/include/prov/provider_ctx.h \
  providers/implementations/include/prov/implementations.h \
  providers/common/include/prov/securitycheck.h
