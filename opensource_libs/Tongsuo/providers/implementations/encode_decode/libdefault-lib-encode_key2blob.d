providers/implementations/encode_decode/libdefault-lib-encode_key2blob.o: \
  providers/implementations/encode_decode/encode_key2blob.c \
  include/internal/deprecated.h include/openssl/configuration.h \
  include/openssl/macros.h include/openssl/opensslconf.h \
  include/openssl/symbol_prefix.h include/openssl/opensslv.h \
  include/openssl/core.h include/openssl/types.h include/openssl/e_os2.h \
  include/openssl/safestack.h include/openssl/stack.h \
  include/openssl/core_dispatch.h include/openssl/core_names.h \
  include/openssl/params.h include/openssl/bn.h include/openssl/crypto.h \
  include/openssl/cryptoerr.h include/openssl/symhacks.h \
  include/openssl/cryptoerr_legacy.h include/openssl/bnerr.h \
  include/openssl/err.h include/openssl/bio.h include/openssl/bioerr.h \
  include/openssl/lhash.h include/openssl/evp.h include/openssl/evperr.h \
  include/openssl/objects.h include/openssl/obj_mac.h \
  include/openssl/asn1.h include/openssl/asn1err.h \
  include/openssl/objectserr.h include/openssl/ec.h \
  include/openssl/ecerr.h include/internal/passphrase.h \
  include/internal/nelem.h \
  providers/implementations/include/prov/implementations.h \
  providers/common/include/prov/bio.h \
  providers/common/include/prov/provider_ctx.h \
  providers/implementations/encode_decode/endecoder_local.h
