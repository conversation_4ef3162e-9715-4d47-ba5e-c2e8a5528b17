providers/implementations/encode_decode/libdefault-lib-endecoder_common.o: \
  providers/implementations/encode_decode/endecoder_common.c \
  include/openssl/core.h include/openssl/types.h include/openssl/e_os2.h \
  include/openssl/macros.h include/openssl/opensslconf.h \
  include/openssl/configuration.h include/openssl/symbol_prefix.h \
  include/openssl/opensslv.h include/openssl/safestack.h \
  include/openssl/stack.h include/openssl/buffer.h \
  include/openssl/crypto.h include/openssl/cryptoerr.h \
  include/openssl/symhacks.h include/openssl/cryptoerr_legacy.h \
  include/openssl/buffererr.h include/internal/asn1.h \
  providers/common/include/prov/bio.h include/openssl/bio.h \
  include/openssl/bioerr.h providers/common/include/prov/provider_ctx.h \
  providers/implementations/encode_decode/endecoder_local.h \
  include/openssl/core_dispatch.h
