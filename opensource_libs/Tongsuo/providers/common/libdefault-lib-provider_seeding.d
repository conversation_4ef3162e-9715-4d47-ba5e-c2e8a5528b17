providers/common/libdefault-lib-provider_seeding.o: \
  providers/common/provider_seeding.c include/openssl/core_dispatch.h \
  include/openssl/core.h include/openssl/types.h include/openssl/e_os2.h \
  include/openssl/macros.h include/openssl/opensslconf.h \
  include/openssl/configuration.h include/openssl/symbol_prefix.h \
  include/openssl/opensslv.h include/openssl/safestack.h \
  include/openssl/stack.h \
  providers/implementations/include/prov/seeding.h \
  providers/common/include/prov/provider_ctx.h include/openssl/crypto.h \
  include/openssl/cryptoerr.h include/openssl/symhacks.h \
  include/openssl/cryptoerr_legacy.h include/openssl/bio.h \
  include/openssl/bioerr.h include/crypto/rand_pool.h \
  include/openssl/rand.h include/openssl/randerr.h include/openssl/evp.h \
  include/openssl/evperr.h include/openssl/params.h include/openssl/bn.h \
  include/openssl/bnerr.h include/openssl/objects.h \
  include/openssl/obj_mac.h include/openssl/asn1.h \
  include/openssl/asn1err.h include/openssl/objectserr.h
